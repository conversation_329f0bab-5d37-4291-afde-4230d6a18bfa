import { writeFile } from 'fs/promises'
import { spawn } from 'child_process'
import path from 'path'

export async function POST(req: Request) {
  const data = await req.formData()
  const file = data.get('file') as File

  const bytes = await file.arrayBuffer()
  const buffer = Buffer.from(bytes)

  const filePath = path.join('/tmp', file.name)
  await writeFile(filePath, buffer)

  return new Promise((resolve) => {
    const whisper = spawn('python3', [
      'scripts/transcribe.py',
      filePath,
    ])

    whisper.stdout.on('data', (data) => {
      console.log(`stdout: ${data}`)
    })

    whisper.stderr.on('data', (data) => {
      console.error(`stderr: ${data}`)
    })

    whisper.on('close', (code) => {
      resolve(Response.json({ message: `Transcription complete.` }))
    })
  })
}