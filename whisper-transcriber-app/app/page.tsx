'use client'

import { useState } from 'react'

export default function HomePage() {
  const [file, setFile] = useState<File|null>(null)
  const [status, setStatus] = useState("")

  const handleUpload = async () => {
    if (!file) return
    setStatus("Uploading...")
    const formData = new FormData()
    formData.append("file", file)

    const res = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    })

    const result = await res.json()
    setStatus(result.message || "Done")
  }

  return (
    <div className="p-4 max-w-xl mx-auto">
      <h1 className="text-xl font-bold mb-4">Whisper Transcriber</h1>
      <input type="file" accept=".mp3,.m4a" onChange={(e) => setFile(e.target.files?.[0] || null)} />
      <button className="mt-4 bg-blue-500 text-white px-4 py-2 rounded" onClick={handleUpload}>Transcribe</button>
      <p className="mt-2 text-sm">{status}</p>
    </div>
  )
}
